# 📖 图像标注工具集 - 用户使用手册

## 🎯 快速开始

### 1. 访问系统
打开浏览器，访问：`http://your-domain.com:5174`

### 2. 首次使用
1. 进入项目管理界面
2. 点击"添加甲方"创建客户信息
3. 点击"新建项目"创建第一个项目
4. 点击"打开项目"开始使用工具

## 🏢 项目管理

### 甲方管理
**添加新甲方**：
- 点击右上角"👥 添加甲方"
- 填写联系人信息、公司名称
- 选择客户类型和所属行业
- 描述具体需求

**管理甲方信息**：
- 在"甲方管理"页面查看所有客户
- 每个甲方卡片显示基本信息和项目数量

### 项目管理
**创建新项目**：
- 点击"➕ 新建项目"
- 输入项目名称和描述
- 选择对应的甲方客户
- 选择项目类型（Flux拼接/Kontext标注/专业标注）
- 设置优先级和截止日期

**项目状态**：
- 🟢 **活跃**: 正在进行的项目
- 🔵 **已完成**: 完成的项目
- ⚫ **非活跃**: 暂停的项目
- 🔴 **已归档**: 归档的项目

## 🛠️ 标注工具

### 🖼️ Flux IC-LoRA 拼接工具
**功能**：水平拼接左图和右图文件夹中的图片

**使用步骤**：
1. 点击"🖼️ 选择左图"上传左侧图片
2. 点击"🖼️ 选择右图"上传右侧图片
3. 设置输出路径
4. 点击"🔗 拼接图片"
5. 预览拼接效果
6. 点击"💾 保存"保存结果

**特色功能**：
- 实时预览拼接效果
- 全局计数器保持编号连续
- 支持撤回操作
- 批量处理多张图片

### 📷 KontextLora 标注工具
**功能**：为图像对添加描述标签

**使用步骤**：
1. 点击"📂 加载原图文件夹"选择原图
2. 点击"📂 加载目标图文件夹"选择目标图
3. 在标签输入框中添加描述
4. 使用方向键或按钮切换图片
5. 点击"💾 保存标签"保存当前标注

**快捷键**：
- ← → 切换图片
- Enter 保存标签
- Ctrl+Z 撤回操作

**特色功能**：
- 双图对比显示
- 固定标签模板
- 自动文件重命名
- 键盘快速导航

### 🎨 专业标注工具
**功能**：专门针对服装图像的多维度标注

**使用步骤**：
1. 点击"📁 选择图片"上传图片
2. 填写触发词
3. 选择服装结构
4. 添加详细描述
5. 设置输出路径
6. 点击"💾 保存标注"

**标注字段**：
- **触发词**: 关键词标签
- **服装结构**: 服装类型分类
- **详细描述**: 详细的文字描述
- **自定义字段**: 可扩展的标注字段

## 🚀 部署管理

### 创建部署配置
1. 进入"🚀 部署管理"页面
2. 点击"➕ 新建部署"
3. 填写部署信息：
   - 部署名称
   - 选择项目
   - 环境类型（开发/测试/生产）
   - 部署平台（Docker/Vercel/AWS等）
   - 域名和端口配置

### 执行部署
1. 在部署卡片中点击"🚀 部署"
2. 系统自动执行部署流程
3. 实时查看部署日志
4. 部署完成后获取访问地址

### 部署状态
- 🟡 **待部署**: 配置已创建，等待部署
- 🔵 **部署中**: 正在执行部署
- 🟢 **成功**: 部署成功，服务正常运行
- 🔴 **失败**: 部署失败，需要检查配置

## 📊 数据管理

### 文件管理
**上传文件**：
- 支持拖拽上传
- 支持批量选择
- 自动文件类型检测

**文件格式**：
- 图片：JPG, PNG, WEBP
- 最大文件大小：50MB
- 批量上传限制：100个文件

### 数据导出
**标注结果**：
- 自动保存到指定路径
- 支持多种命名规则
- 生成配套的标签文件

**项目数据**：
- 项目配置自动保存
- 支持数据备份和恢复
- 历史记录追踪

## ⚙️ 系统设置

### 个性化配置
**输出路径**：
- 设置默认保存路径
- 支持相对路径和绝对路径
- 自动创建目录结构

**标签模板**：
- 创建常用标签模板
- 快速应用预设标签
- 支持标签历史记录

### 性能优化
**图片处理**：
- 自动压缩大图片
- 智能缓存机制
- 并行处理支持

**存储管理**：
- 定期清理临时文件
- 监控磁盘使用情况
- 自动备份重要数据

## 🔧 故障排除

### 常见问题

**1. 图片无法上传**
- 检查文件格式是否支持
- 确认文件大小不超过限制
- 检查网络连接状态

**2. 标注无法保存**
- 确认输出路径有写入权限
- 检查磁盘空间是否充足
- 验证文件名是否合法

**3. 工具切换失败**
- 刷新页面重试
- 清除浏览器缓存
- 检查服务器连接状态

**4. 部署失败**
- 检查部署配置是否正确
- 确认目标环境可访问
- 查看详细错误日志

### 获取帮助
**在线支持**：
- 邮箱：<EMAIL>
- GitHub：https://github.com/yhsnba/Feature-plug-in-collection

**自助诊断**：
- 查看浏览器控制台错误
- 检查网络连接状态
- 验证系统配置

## 💡 使用技巧

### 效率提升
1. **使用快捷键**：熟练使用键盘快捷键提高操作效率
2. **批量处理**：充分利用批量上传和处理功能
3. **模板复用**：创建标签模板避免重复输入
4. **项目分类**：合理组织项目结构便于管理

### 最佳实践
1. **定期备份**：重要数据及时备份
2. **规范命名**：使用统一的文件命名规则
3. **质量控制**：定期检查标注质量
4. **团队协作**：建立标注规范和流程

---

**图像标注工具集** - 让图像标注工作更高效、更专业！🚀

如有疑问，请参考部署指南或联系技术支持。
